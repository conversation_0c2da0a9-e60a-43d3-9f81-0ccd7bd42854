"""
Music Detector检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class MusicDetector(BaseAppDetector):
    """音乐应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.MUSIC)
    
    def get_package_names(self) -> List[str]:
        """获取音乐应用包名列表"""
        return [
            "com.visha.media",
            "com.transsion.magicshow",
            "com.transsion.visha",
            "com.visha.player",
            "com.visha",
            "com.android.media",
            "com.google.android.media",
            "com.spotify.media",
            "com.netease.cloudmusic",
            "com.tencent.qqmusic",
            "com.kugou.android",
            "com.kuwo.kwmusic",
            "com.miui.player",
            "com.samsung.android.media",
            "com.huawei.media",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取音乐应用关键词列表"""
        return ["media", "player", "visha", "音乐", "播放器"]
