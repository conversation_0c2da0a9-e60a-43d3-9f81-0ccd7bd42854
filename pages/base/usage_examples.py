"""
应用检测器使用示例
展示如何使用重构后的应用检测器
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

def example_basic_usage():
    """基本使用示例"""
    print("📱 基本使用示例")
    print("-" * 40)
    
    try:
        from app_detector import AppDetector, AppType
        
        # 创建检测器实例
        detector = AppDetector()
        
        # 方式1: 使用枚举类型（推荐）
        print("✅ 使用枚举类型:")
        print(f"   天气应用: detector.check_app_opened(AppType.WEATHER)")
        print(f"   相机应用: detector.check_app_opened(AppType.CAMERA)")
        
        # 方式2: 使用字符串
        print("\n✅ 使用字符串:")
        print(f"   音乐应用: detector.check_app_opened('media')")
        print(f"   设置应用: detector.check_app_opened('settings')")
        
        return True
    except Exception as e:
        print(f"❌ 基本使用示例失败: {e}")
        return False

def example_backward_compatibility():
    """向后兼容示例"""
    print("\n🔄 向后兼容示例")
    print("-" * 40)
    
    try:
        from app_detector import AppDetector
        
        detector = AppDetector()
        
        print("✅ 原有方法仍然可用:")
        print("   detector.check_app_opened(AppType.WEATHER)")
        print("   detector.check_app_opened(AppType.CAMERA)")
        print("   detector.check_app_opened(AppType.MUSIC)")
        print("   detector.check_app_opened(AppType.CONTACTS)")
        print("   detector.check_app_opened(AppType.FACEBOOK)")
        
        print("\n💡 现有代码无需任何修改！")
        
        return True
    except Exception as e:
        print(f"❌ 向后兼容示例失败: {e}")
        return False

def example_advanced_features():
    """高级功能示例"""
    print("\n🚀 高级功能示例")
    print("-" * 40)
    
    try:
        from app_detector import AppDetector, AppType
        
        detector = AppDetector()
        
        print("✅ 运行状态摘要:")
        print("   summary = detector.get_running_apps_summary()")
        print("   # 返回: {'weather': False, 'camera': True, ...}")
        
        print("\n✅ 前台检测:")
        print("   detector.check_app_in_foreground(AppType.MUSIC)")
        
        print("\n✅ 应用版本:")
        print("   version = detector.get_app_version(AppType.CAMERA)")
        
        print("\n✅ 设备信息:")
        print("   info = detector.get_device_info()")
        print("   # 返回: {'model': 'xxx', 'brand': 'xxx', ...}")
        
        print("\n✅ 查找可用应用:")
        print("   apps = detector.find_available_apps('media')")
        
        return True
    except Exception as e:
        print(f"❌ 高级功能示例失败: {e}")
        return False

def example_alarm_features():
    """闹钟功能示例"""
    print("\n⏰ 闹钟功能示例")
    print("-" * 40)
    
    try:
        from app_detector import AppDetector
        
        detector = AppDetector()
        
        print("✅ 闹钟状态检查:")
        print("   has_alarm = detector.check_alarm_status()")
        
        print("\n✅ 获取闹钟列表:")
        print("   alarms = detector.get_alarm_list()")
        print("   # 返回: [{'time': '08:30', 'enabled': True}, ...]")
        
        print("\n✅ 设置闹钟:")
        print("   success = detector.set_alarm(8, 30)  # 8:30")
        
        print("\n✅ 清除所有闹钟:")
        print("   success = detector.clear_all_alarms()")
        
        print("\n✅ 获取下一个闹钟:")
        print("   next_alarm = detector.get_next_alarm_info()")
        
        return True
    except Exception as e:
        print(f"❌ 闹钟功能示例失败: {e}")
        return False

def example_custom_detector():
    """自定义检测器示例"""
    print("\n🔧 自定义检测器示例")
    print("-" * 40)
    
    print("✅ 创建新的应用检测器:")
    print("""
# 1. 创建新文件: detectors/my_app_detector.py
from typing import List
from app_detector import BaseAppDetector, AppType

class MyAppDetector(BaseAppDetector):
    def __init__(self):
        super().__init__(AppType.CUSTOM)  # 需要先添加到枚举
    
    def get_package_names(self) -> List[str]:
        return ["com.mycompany.myapp"]
    
    def get_keywords(self) -> List[str]:
        return ["myapp", "my_app"]

# 2. 在AppDetector中注册
# 在_init_detectors方法中添加:
# self._detectors[AppType.CUSTOM] = MyAppDetector()
""")
    
    return True

def example_error_handling():
    """错误处理示例"""
    print("\n🛡️ 错误处理示例")
    print("-" * 40)
    
    print("✅ 推荐的错误处理模式:")
    print("""
try:
    detector = AppDetector()
    
    # 检查应用状态
    if detector.check_app_opened(AppType.WEATHER):
        print("天气应用正在运行")
    else:
        print("天气应用未运行")
        
except Exception as e:
    print(f"检测失败: {e}")
    # 可以使用备用检测方法或记录错误
""")
    
    return True

def main():
    """主示例函数"""
    print("📚 应用检测器使用示例")
    print("=" * 60)
    
    examples = [
        ("基本使用", example_basic_usage),
        ("向后兼容", example_backward_compatibility),
        ("高级功能", example_advanced_features),
        ("闹钟功能", example_alarm_features),
        ("自定义检测器", example_custom_detector),
        ("错误处理", example_error_handling),
    ]
    
    success_count = 0
    
    for example_name, example_func in examples:
        try:
            if example_func():
                success_count += 1
        except Exception as e:
            print(f"❌ {example_name}示例失败: {e}")
    
    print(f"\n📋 示例总结")
    print("=" * 60)
    print(f"✅ 完成示例: {success_count}/{len(examples)} 个")
    
    print(f"\n🎯 关键要点:")
    print("   • 新项目推荐使用 AppType 枚举")
    print("   • 现有项目可继续使用原有方法")
    print("   • 利用高级功能提升开发效率")
    print("   • 适当的错误处理确保稳定性")
    print("   • 扩展新应用类型很简单")
    
    print(f"\n📖 更多信息:")
    print("   • 查看 README.md 了解详细架构")
    print("   • 查看 detectors/ 目录了解各检测器")
    print("   • 运行 demo_refactored.py 查看完整演示")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    main()
