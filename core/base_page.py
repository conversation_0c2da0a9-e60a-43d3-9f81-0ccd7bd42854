"""
页面基类
实现PO模式的页面对象基类
"""
import time
from typing import Optional, Dict, Any
from core.logger import log
from core.base_driver import driver_manager
from core.base_element import BaseElement
from utils.yaml_utils import YamlUtils


class BasePage:
    """页面对象基类"""
    
    def __init__(self, app_name: str = "", page_name: str = "", driver=None):
        """
        初始化页面对象

        Args:
            app_name: 应用名称
            page_name: 页面名称
            driver: 设备驱动实例
        """
        self.app_name = app_name
        self.page_name = page_name
        self.driver = driver if driver is not None else driver_manager.driver
        
        # 加载配置
        try:
            config_path = YamlUtils.get_config_path("config.yaml")
            self.config = YamlUtils.load_yaml(config_path)
            self.timeout = self.config.get("app", {}).get("page_load_timeout", 30)
        except Exception as e:
            log.warning(f"加载配置失败: {e}")
            self.config = {}
            self.timeout = 30
        
        log.info(f"初始化页面: {self.app_name} - {self.page_name}")
    
    def get_app_config(self) -> Dict[str, Any]:
        """
        获取应用配置
        
        Returns:
            Dict: 应用配置字典
        """
        apps_config = self.config.get("apps", {})
        return apps_config.get(self.app_name.lower(), {})
    
    def start_app(self) -> bool:
        """
        启动应用
        
        Returns:
            bool: 启动是否成功
        """
        try:
            app_config = self.get_app_config()
            package_name = app_config.get("package_name")
            
            if not package_name:
                log.error(f"未找到应用 {self.app_name} 的包名配置")
                return False
            
            log.info(f"启动应用: {self.app_name} ({package_name})")
            driver_manager.start_app(package_name)
            
            # 等待页面加载
            time.sleep(2)
            return True
            
        except Exception as e:
            log.error(f"启动应用失败: {e}")
            return False
    
    def stop_app(self) -> bool:
        """
        停止应用
        
        Returns:
            bool: 停止是否成功
        """
        try:
            app_config = self.get_app_config()
            package_name = app_config.get("package_name")
            
            if not package_name:
                log.error(f"未找到应用 {self.app_name} 的包名配置")
                return False
            
            log.info(f"停止应用: {self.app_name} ({package_name})")
            driver_manager.stop_app(package_name)
            return True
            
        except Exception as e:
            log.error(f"停止应用失败: {e}")
            return False
    
    def is_app_running(self) -> bool:
        """
        检查应用是否在运行
        
        Returns:
            bool: 应用是否在运行
        """
        try:
            app_config = self.get_app_config()
            package_name = app_config.get("package_name")
            
            if not package_name:
                return False
            
            return driver_manager.is_app_running(package_name)
            
        except Exception as e:
            log.error(f"检查应用状态失败: {e}")
            return False
    
    def wait_for_page_load(self, timeout: Optional[float] = None) -> bool:
        """
        等待页面加载完成
        子类应该重写此方法，实现具体的页面加载判断逻辑
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: 页面是否加载完成
        """
        timeout = timeout or self.timeout
        log.info(f"等待页面加载: {self.page_name}, 超时时间: {timeout}秒")
        
        # 默认实现：简单等待
        time.sleep(2)
        return True
    
    def screenshot(self, filename: Optional[str] = None, use_test_class_dir: bool = True) -> str:
        """
        页面截图

        Args:
            filename: 截图文件名
            use_test_class_dir: 是否使用测试类名称作为子目录

        Returns:
            str: 截图文件路径
        """
        if filename is None:
            from utils.file_utils import FileUtils
            filename = FileUtils.get_screenshot_name(f"{self.app_name}_{self.page_name}")

        return driver_manager.screenshot(filename, use_test_class_dir)
    
    def swipe_up(self, duration: float = 0.5) -> None:
        """
        向上滑动
        
        Args:
            duration: 滑动持续时间
        """
        try:
            width, height = driver_manager.get_window_size()
            start_x = width // 2
            start_y = height * 3 // 4
            end_x = width // 2
            end_y = height // 4
            
            self.driver.swipe(start_x, start_y, end_x, end_y, duration=duration)
            log.info("向上滑动")
            
        except Exception as e:
            log.error(f"向上滑动失败: {e}")

    # 从底部向上滑动
    def swipe_up_from_bottom(self, duration: float = 0.5) -> None:
        """
        从底部向上滑动

        Args:
            duration: 滑动持续时间
        """
        try:
            width, height = driver_manager.get_window_size()
            start_x = width -10
            start_y = height  - 10
            end_x = width -10
            end_y = height  - 100

            self.driver.swipe(start_x, start_y, end_x, end_y, duration=duration)
            log.info("从底部向上滑动")

        except Exception as e:
            log.error(f"从底部向上滑动失败: {e}")
    
    def swipe_down(self, duration: float = 0.5) -> None:
        """
        向下滑动
        
        Args:
            duration: 滑动持续时间
        """
        try:
            width, height = driver_manager.get_window_size()
            start_x = width // 2
            start_y = height // 4
            end_x = width // 2
            end_y = height * 3 // 4
            
            self.driver.swipe(start_x, start_y, end_x, end_y, duration=duration)
            log.info("向下滑动")
            
        except Exception as e:
            log.error(f"向下滑动失败: {e}")
    
    def swipe_left(self, duration: float = 0.5) -> None:
        """
        向左滑动
        
        Args:
            duration: 滑动持续时间
        """
        try:
            width, height = driver_manager.get_window_size()
            start_x = width * 3 // 4
            start_y = height // 2
            end_x = width // 4
            end_y = height // 2
            
            self.driver.swipe(start_x, start_y, end_x, end_y, duration=duration)
            log.info("向左滑动")
            
        except Exception as e:
            log.error(f"向左滑动失败: {e}")
    
    def swipe_right(self, duration: float = 0.5) -> None:
        """
        向右滑动
        
        Args:
            duration: 滑动持续时间
        """
        try:
            width, height = driver_manager.get_window_size()
            start_x = width // 4
            start_y = height // 2
            end_x = width * 3 // 4
            end_y = height // 2
            
            self.driver.swipe(start_x, start_y, end_x, end_y, duration=duration)
            log.info("向右滑动")
            
        except Exception as e:
            log.error(f"向右滑动失败: {e}")
    
    def press_back(self) -> None:
        """按返回键"""
        try:
            self.driver.press("back")
            log.info("按返回键")
        except Exception as e:
            log.error(f"按返回键失败: {e}")
    
    def press_home(self) -> None:
        """按Home键"""
        try:
            self.driver.press("home")
            log.info("按Home键")
        except Exception as e:
            log.error(f"按Home键失败: {e}")
    
    def press_menu(self) -> None:
        """按菜单键"""
        try:
            self.driver.press("menu")
            log.info("按菜单键")
        except Exception as e:
            log.error(f"按菜单键失败: {e}")

    def press_power(self) -> None:
        """按电源键"""
        try:
            self.driver.press("power")
            log.info("按电源键")
        except Exception as e:
            log.error(f"按电源键失败: {e}")

    def is_screen_on(self) -> bool:
        """检查屏幕是否锁定"""
        try:
            log.info("检查屏幕是否开启")
            result = driver_manager.screen_on()
            log.info(f"屏幕开启状态: {result}")
            return result
        except Exception as e:
            log.error(f"检查屏幕是否锁定失败: {e}")
            return False
    
    def create_element(self, locator: dict, description: str = "") -> BaseElement:
        """
        创建元素对象
        
        Args:
            locator: 元素定位器
            description: 元素描述
            
        Returns:
            BaseElement: 元素对象
        """
        if not description:
            description = f"{self.page_name}_{str(locator)}"
        
        return BaseElement(locator, description)

    def weak_up_screen(self) -> None:
        """唤醒屏幕"""
        try:
            log.info("唤醒屏幕")
            if not self.is_screen_on():
                self.press_power()
                self.swipe_up()
            else:
                self.swipe_up()
                self.press_home()
                log.info("屏幕已开启")
        except Exception as e:
            log.error(f"唤醒屏幕失败: {e}")

    def get_recent_key_resource_id(self) -> str:
        """获取recent键的resourceId"""
        try:
            config = self.config.get("recent_key_resource_id")
            resourceId = config.get(driver_manager.get_device_info().get("brand")).get("resourceId")
            return resourceId
        except Exception as e:
            log.error(f"获取recent键的resourceId失败: {e}")
            return ""
    def get_recent_delete_resource_id(self) -> str:
        """获取recent删除键的resourceId"""
        try:
            config = self.config.get("recent_delete_resource_id")
            resourceId = config.get(driver_manager.get_device_info().get("brand")).get("resourceId")
            log.info(f"recent删除键的resourceId: {resourceId}")
            return resourceId
        except Exception as e:
            log.error(f"获取recent删除键的resourceId失败: {e}")
            return ""
    def is_three_navigation(self) -> bool:
        """检查是否为三键导航"""
        try:
            log.info("检查是否为三键导航")
            resourceId = self.get_recent_key_resource_id()
            result = BaseElement({"resourceId": resourceId}, "recent键").is_exists()
            log.info(f"recent键的resourceId: {resourceId}")
            log.info(f"是否为三键导航: {result}")
            return result
        except Exception as e:
            log.error(f"检查是否为三键导航失败: {e}")
            return False

    def clear_app_data(self, package_name: str) -> bool:
        """清除应用数据"""
        try:
            log.info(f"清除应用数据: {package_name}")
            self.driver.app_clear(package_name)
            return True
        except Exception as e:
            log.error(f"清除应用数据失败: {e}")
            return False

   # 通过recent键清理进程
    def clear_recent_apps(self) -> bool:
        """
        清理Recent页面应用
        使用AdbProcessMonitor进行Recent页面清理
        """
        try:
            # 判断当前导航模式  三键导航有recent键 手势导航没有recent键
            resourceId = self.get_recent_delete_resource_id()
            if self.is_three_navigation():
                # 三键导航
                log.info("点击recent键")
                self.driver.press("recent")
                log.info("点击删除键")

                if BaseElement({"resourceId": resourceId}, "recent删除键").is_exists():
                    self.driver(resourceId=resourceId).click()
                else:
                    log.info("recent中不存在应用")
                    self.driver.press("back")
            else:
                # 手势导航
                log.info("通过手势清理recent")
                self.swipe_up_from_bottom()
                time.sleep(1)
                if BaseElement({"resourceId": resourceId}, "recent删除键").is_exists():
                    log.info("recent删除键存在")
                    self.driver(resourceId=resourceId).click()
                else:
                    log.info("recent中不存在应用")
                    self.driver.press("back")
            return True
        except Exception as e:
            log.error(f"❌ Recent页面清理异常: {e}")
            return False


if __name__ == '__main__':
    page = BasePage("Ella", "Dialogue")
    # page.clear_recent_apps()
    # page.swipe_up_from_bottom()
    # package_name = 'com.transsion.aivoiceassistant'
    # page.clear_app_data(package_name=package_name)
    page.weak_up_screen()
