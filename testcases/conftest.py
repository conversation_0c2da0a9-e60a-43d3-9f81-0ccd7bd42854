"""
设备感知的pytest配置
支持多设备并行测试
"""
import os
import sys
import pytest
import allure
from core.base_driver import driver_manager
from utils.file_utils import FileUtils
from utils.yaml_utils import YamlUtils

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import log


def pytest_addoption(parser):
    """添加pytest命令行选项"""
    parser.addoption(
        "--device-id",
        action="store",
        default=None,
        help="指定测试设备ID"
    )


@pytest.fixture(scope="session")
def setup_test_environment():
    """
    测试环境初始化
    在整个测试会话开始前执行一次
    """
    log.info("=" * 50)
    log.info("开始测试会话")
    log.info("=" * 50)

    # 确保必要的目录存在
    project_root = YamlUtils.get_project_root()

    # 创建报告目录
    FileUtils.ensure_dir(f"{project_root}/reports/screenshots")
    FileUtils.ensure_dir(f"{project_root}/reports/allure-results")
    FileUtils.ensure_dir(f"{project_root}/logs")

    # 设置屏幕为永不超时


    # 清理Ella应用数据
    from pages.apps.ella.dialogue_page import EllaDialoguePage
    ella_page = EllaDialoguePage()
    # 唤醒屏幕
    log.info("唤醒屏幕")
    ella_page.weak_up_screen()
    # 初始化Ella应用
    log.info("初始化Ella应用")
    ella_page.init_ella()


    from testcases.test_ella.base_ella_test import BaseEllaTest
    base_test = BaseEllaTest()
    # 设置屏幕永不超时
    log.info("设置屏幕永不超时")
    base_test.setup_batch_test_screen()

    # 获取设备信息
    try:
        device_info = driver_manager.get_device_info()
        log.info(f"测试设备信息: {device_info}")
        # 将设备信息添加到Allure报告
        allure.dynamic.feature("设备信息")
        allure.dynamic.story(f"设备型号: {device_info.get('model', 'Unknown')}")
        allure.dynamic.description(f"设备详情: {device_info}")

    except Exception as e:
        log.error(f"获取设备信息失败: {e}")

    yield

    log.info("=" * 50)
    log.info("测试会话结束")
    log.info("=" * 50)

@pytest.fixture(scope="function")
def ella_app():
    """设备感知的Ella应用fixture"""
    # 获取设备ID
    device_id = os.environ.get("PYTEST_DEVICE_ID")
    log.info(f"设备ID: {device_id}")
    
    if device_id:
        log.info(f"🔧 使用指定设备: {device_id}")
        # 使用多设备版本
        from temp.multi_device_base_test import MultiDeviceSimpleEllaTest
        test_instance = MultiDeviceSimpleEllaTest(device_id=device_id)
        ella_app_generator = test_instance.ella_app()
        ella_app = next(ella_app_generator)
        
        try:
            yield ella_app
        finally:
            try:
                next(ella_app_generator)
            except StopIteration:
                pass
    else:
        log.info("🔧 使用默认设备")
        # 使用原始版本
        from pages.apps.ella.dialogue_page import EllaDialoguePage
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        base_test = BaseEllaTest()
        # base_test.setup_batch_test_screen()
        
        ella_page = EllaDialoguePage()
        
        try:
            base_test.clear_all_running_processes(ella_page)
            assert ella_page.start_app(), "Ella应用启动失败"
            assert ella_page.wait_for_page_load(timeout=15), "Ella页面加载失败"
            
            log.info("✅ Ella应用启动成功")
            yield ella_page
            
        except Exception as e:
            log.error(f"❌ Ella应用启动异常: {e}")
            pytest.fail(f"Ella应用启动异常: {e}")
        finally:
            try:
                ella_page.stop_app()
            except Exception as e:
                log.warning(f"⚠️ 停止应用异常: {e}")
