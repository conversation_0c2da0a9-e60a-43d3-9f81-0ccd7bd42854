import os
import sys
import requests
from typing import Dict, Any, <PERSON><PERSON>, Optional

# === input params start
app_id = os.getenv("APP_ID")  # app_id, 应用 ID, required
app_secret = os.getenv("APP_SECRET")  # app_secret, 应用密钥, required
folder_token = os.getenv("FOLDER_TOKEN")  # string, 目标文件夹 token, required
file_path = os.getenv("FILE_PATH")  # string, 本地文件路径, required
user_access_token = os.getenv("USER_ACCESS_TOKEN")  # uat, 用户访问凭证, optional, 仅当需要以用户身份上传时填写


# === input params end

def get_tenant_access_token(app_id: str, app_secret: str) -> Tuple[str, Optional[Exception]]:
    """获取 tenant_access_token

    Args:
        app_id: 应用ID
        app_secret: 应用密钥

    Returns:
        Tuple[str, Optional[Exception]]: (access_token, error)
    """
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    payload = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }
    try:
        print(f"POST: {url}, payload: {{'app_id': '***', 'app_secret': '***'}}")
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
        result = response.json()
        print(f"tenant_access_token response: {result}")
        if result.get("code", 0) != 0:
            print(f"ERROR: failed to get tenant_access_token: {result.get('msg', 'unknown error')}", file=sys.stderr)
            return "", Exception(f"failed to get tenant_access_token: {response.text}")
        return result["tenant_access_token"], None
    except Exception as e:
        print(f"ERROR: getting tenant_access_token: {e}", file=sys.stderr)
        return "", e


def upload_file_to_folder(
        access_token: str,
        file_path: str,
        folder_token: str,
        use_user_token: bool = False
) -> Optional[str]:
    """将本地文件上传至飞书指定文件夹

    Args:
        access_token: 访问凭证（tenant_access_token 或 user_access_token）
        file_path: 本地文件路径
        folder_token: 目标文件夹 token
        use_user_token: 是否使用 user_access_token

    Returns:
        Optional[str]: 上传成功后返回 file_token，否则返回 None
    """
    url = "https://open.feishu.cn/open-apis/drive/v1/files/upload_all"
    try:
        if not os.path.isfile(file_path):
            print(f"ERROR: 文件不存在: {file_path}", file=sys.stderr)
            return None
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            print(f"ERROR: 不可上传空文件: {file_path}", file=sys.stderr)
            return None

        print(f"准备上传文件: {file_path}, 大小: {file_size} 字节, 目标文件夹: {folder_token}")

        # 20MB以内直接上传
        if file_size <= 20 * 1024 * 1024:
            with open(file_path, "rb") as f:
                files = {
                    "file": (os.path.basename(file_path), f, "application/octet-stream")
                }
                data = {
                    "file_name": os.path.basename(file_path),
                    "parent_type": "explorer",
                    "parent_node": folder_token,
                    "size": str(file_size)
                }
                headers = {
                    "Authorization": f"Bearer {access_token}"
                }
                print(f"POST: {url}, headers: {{'Authorization': 'Bearer ***'}}, data: {data}")
                response = requests.post(url, headers=headers, data=data, files=files)
                print(f"upload_all response status: {response.status_code}")
                try:
                    result = response.json()
                except Exception:
                    print(f"ERROR: 非JSON响应: {response.text}", file=sys.stderr)
                    return None
                print(f"upload_all response: {result}")
                if result.get("code", 0) != 0:
                    print(f"ERROR: 上传失败, HTTPCode: {response.status_code}, responseBody: {response.text}",
                          file=sys.stderr)
                    return None
                file_token = result["data"]["file_token"]
                print(f"文件上传成功, file_token: {file_token}")
                return file_token
        else:
            # 分片上传
            print("文件大于20MB，采用分片上传")
            upload_id, block_size, block_num = prepare_multipart_upload(access_token, file_path, folder_token)
            if not upload_id:
                print("ERROR: 分片上传预处理失败", file=sys.stderr)
                return None
            if not upload_parts(access_token, file_path, upload_id, block_size, block_num):
                print("ERROR: 分片上传失败", file=sys.stderr)
                return None
            file_token = finish_multipart_upload(access_token, upload_id, block_num)
            if not file_token:
                print("ERROR: 分片上传完成失败", file=sys.stderr)
                return None
            print(f"文件分片上传成功, file_token: {file_token}")
            return file_token
    except Exception as e:
        print(f"ERROR: 上传文件异常: {e}", file=sys.stderr)
        return None


def prepare_multipart_upload(
        access_token: str,
        file_path: str,
        folder_token: str
) -> Tuple[Optional[str], Optional[int], Optional[int]]:
    """分片上传预处理，获取 upload_id 和分片策略

    Args:
        access_token: 访问凭证
        file_path: 本地文件路径
        folder_token: 目标文件夹 token

    Returns:
        Tuple[upload_id, block_size, block_num]
    """
    url = "https://open.feishu.cn/open-apis/drive/v1/files/upload_prepare"
    file_size = os.path.getsize(file_path)
    payload = {
        "file_name": os.path.basename(file_path),
        "parent_type": "explorer",
        "parent_node": folder_token,
        "size": file_size
    }
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json; charset=utf-8"
    }
    try:
        print(f"POST: {url}, headers: {{'Authorization': 'Bearer ***'}}, payload: {payload}")
        response = requests.post(url, headers=headers, json=payload)
        print(f"upload_prepare response status: {response.status_code}")
        result = response.json()
        print(f"upload_prepare response: {result}")
        if result.get("code", 0) != 0:
            print(f"ERROR: 分片上传预处理失败, HTTPCode: {response.status_code}, responseBody: {response.text}",
                  file=sys.stderr)
            return None, None, None
        data = result["data"]
        return data["upload_id"], data["block_size"], data["block_num"]
    except Exception as e:
        print(f"ERROR: 分片上传预处理异常: {e}", file=sys.stderr)
        return None, None, None


def upload_parts(
        access_token: str,
        file_path: str,
        upload_id: str,
        block_size: int,
        block_num: int
) -> bool:
    """分片上传文件内容

    Args:
        access_token: 访问凭证
        file_path: 本地文件路径
        upload_id: 分片上传事务ID
        block_size: 分片大小
        block_num: 分片数量

    Returns:
        bool: 是否全部分片上传成功
    """
    url = "https://open.feishu.cn/open-apis/drive/v1/files/upload_part"
    try:
        with open(file_path, "rb") as f:
            for seq in range(block_num):
                part_data = f.read(block_size)
                if not part_data:
                    print(f"ERROR: 读取分片数据失败, seq: {seq}", file=sys.stderr)
                    return False
                files = {
                    "file": (os.path.basename(file_path), part_data, "application/octet-stream")
                }
                data = {
                    "upload_id": upload_id,
                    "seq": str(seq),
                    "size": str(len(part_data))
                }
                headers = {
                    "Authorization": f"Bearer {access_token}"
                }
                print(f"POST: {url}, headers: {{'Authorization': 'Bearer ***'}}, data: {data}, seq: {seq}")
                response = requests.post(url, headers=headers, data=data, files=files)
                print(f"upload_part response status: {response.status_code}")
                try:
                    result = response.json()
                except Exception:
                    print(f"ERROR: 非JSON响应: {response.text}", file=sys.stderr)
                    return False
                print(f"upload_part response: {result}")
                if result.get("code", 0) != 0:
                    print(
                        f"ERROR: 分片上传失败, seq: {seq}, HTTPCode: {response.status_code}, responseBody: {response.text}",
                        file=sys.stderr)
                    return False
        return True
    except Exception as e:
        print(f"ERROR: 分片上传异常: {e}", file=sys.stderr)
        return False


def finish_multipart_upload(
        access_token: str,
        upload_id: str,
        block_num: int
) -> Optional[str]:
    """分片上传完成，获取 file_token

    Args:
        access_token: 访问凭证
        upload_id: 分片上传事务ID
        block_num: 分片数量

    Returns:
        Optional[str]: 上传成功后返回 file_token，否则返回 None
    """
    url = "https://open.feishu.cn/open-apis/drive/v1/files/upload_finish"
    payload = {
        "upload_id": upload_id,
        "block_num": block_num
    }
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json; charset=utf-8"
    }
    try:
        print(f"POST: {url}, headers: {{'Authorization': 'Bearer ***'}}, payload: {payload}")
        response = requests.post(url, headers=headers, json=payload)
        print(f"upload_finish response status: {response.status_code}")
        result = response.json()
        print(f"upload_finish response: {result}")
        if result.get("code", 0) != 0:
            print(f"ERROR: 分片上传完成失败, HTTPCode: {response.status_code}, responseBody: {response.text}",
                  file=sys.stderr)
            return None
        file_token = result["data"]["file_token"]
        return file_token
    except Exception as e:
        print(f"ERROR: 分片上传完成异常: {e}", file=sys.stderr)
        return None


def main():
    # 参数校验
    pass
