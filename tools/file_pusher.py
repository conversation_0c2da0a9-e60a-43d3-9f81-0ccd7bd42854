"""
文件推送工具
用于将测试文件推送到Android设备
"""
import os
import subprocess
import time
from pathlib import Path
from typing import List, Optional
from core.logger import log


class FilePusher:
    """文件推送工具类"""

    def __init__(self):
        """初始化文件推送工具"""
        self.project_root = Path(__file__).parent.parent
        self.static_data_path = self.project_root / "data" / "static"

    def _run_adb_command(self, cmd: str) -> subprocess.CompletedProcess:
        """
        执行ADB命令，统一处理编码问题

        Args:
            cmd: ADB命令字符串

        Returns:
            subprocess.CompletedProcess: 命令执行结果
        """
        try:
            # 使用UTF-8编码并忽略错误字符，避免GBK编码问题
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=30  # 添加超时防止命令卡死
            )
            return result
        except subprocess.TimeoutExpired:
            log.error(f"ADB命令执行超时: {cmd}")
            # 返回一个失败的结果
            return subprocess.CompletedProcess(cmd, 1, "", "命令执行超时")
        except Exception as e:
            log.error(f"执行ADB命令时出错: {e}")
            return subprocess.CompletedProcess(cmd, 1, "", str(e))
        
    def push_documents_to_device(self, target_path: str = "/sdcard/Download") -> bool:
        """
        推送文档文件到设备
        
        Args:
            target_path: 目标路径，默认为/sdcard/Download
            
        Returns:
            bool: 推送是否成功
        """
        try:
            log.info("开始推送文档文件到设备")
            
            document_folder = self.static_data_path / "document"
            if not document_folder.exists():
                log.error(f"文档文件夹不存在: {document_folder}")
                return False
            
            # 获取所有文档文件
            document_files = self._get_document_files(document_folder)
            if not document_files:
                log.warning("未找到任何文档文件")
                return True
            
            # 确保目标目录存在
            if not self._ensure_target_directory(target_path):
                log.error(f"无法创建目标目录: {target_path}")
                return False
            
            # 推送每个文件
            success_count = 0
            for file_path in document_files:
                if self._push_single_file(file_path, target_path):
                    success_count += 1
                else:
                    log.warning(f"推送文件失败: {file_path}")
            
            log.info(f"文档推送完成: {success_count}/{len(document_files)} 个文件成功")
            return success_count > 0
            
        except Exception as e:
            log.error(f"推送文档文件失败: {e}")
            return False
    
    def push_images_to_device(self, target_path: str = "/sdcard/DCIM/Camera") -> bool:
        """
        推送图片文件到设备
        
        Args:
            target_path: 目标路径，默认为/sdcard/DCIM/Camera
            
        Returns:
            bool: 推送是否成功
        """
        try:
            log.info("开始推送图片文件到设备")
            
            gallery_folder = self.static_data_path / "gallery"
            if not gallery_folder.exists():
                log.error(f"图片文件夹不存在: {gallery_folder}")
                return False
            
            # 获取所有图片文件
            image_files = self._get_image_files(gallery_folder)
            if not image_files:
                log.warning("未找到任何图片文件")
                return True
            
            # 确保目标目录存在
            if not self._ensure_target_directory(target_path):
                log.error(f"无法创建目标目录: {target_path}")
                return False
            
            # 推送每个文件
            success_count = 0
            for file_path in image_files:
                if self._push_single_file(file_path, target_path):
                    success_count += 1
                else:
                    log.warning(f"推送文件失败: {file_path}")

            # 刷新媒体库，让相册能看到新推送的图片
            if success_count > 0:
                self._refresh_media_library(target_path)

            log.info(f"图片推送完成: {success_count}/{len(image_files)} 个文件成功")
            return success_count > 0
            
        except Exception as e:
            log.error(f"推送图片文件失败: {e}")
            return False

    def push_ask_screen_image(self, image_name: str, target_path: str = "/sdcard/Download") -> bool:
        """
        从static/ask_screen推送指定名称的图片到设备

        Args:
            image_name: 图片文件名（支持带扩展名或不带扩展名）
            target_path: 目标路径，默认为/sdcard/Download

        Returns:
            bool: 推送是否成功
        """
        try:
            log.info(f"开始推送ask_screen图片: {image_name} 到 {target_path}")

            # 构建ask_screen文件夹路径
            ask_screen_folder = self.static_data_path / "ask_screen"
            if not ask_screen_folder.exists():
                log.error(f"ask_screen文件夹不存在: {ask_screen_folder}")
                return False

            # 查找指定名称的图片文件
            image_file = self._find_ask_screen_image(ask_screen_folder, image_name)
            if not image_file:
                log.error(f"未找到图片文件: {image_name}")
                return False

            log.info(f"找到图片文件: {image_file}")

            # 确保目标目录存在
            if not self._ensure_target_directory(target_path):
                log.error(f"无法创建目标目录: {target_path}")
                return False

            # 推送文件
            if self._push_single_file(image_file, target_path):
                log.info(f"✅ 成功推送图片: {image_file.name} 到 {target_path}")

                # 刷新媒体库，让系统能识别新文件
                self._refresh_media_library(target_path)
                return True
            else:
                log.error(f"❌ 推送图片失败: {image_file}")
                return False

        except Exception as e:
            log.error(f"推送ask_screen图片失败: {e}")
            return False

    def _find_ask_screen_image(self, folder_path: Path, image_name: str) -> Optional[Path]:
        """
        在ask_screen文件夹中查找指定名称的图片文件
        支持多种匹配策略：精确匹配、下划线替换匹配、模糊匹配

        Args:
            folder_path: ask_screen文件夹路径
            image_name: 图片文件名（如 "add this number"）

        Returns:
            Optional[Path]: 找到的图片文件路径，未找到返回None
        """
        try:
            # 支持的图片扩展名
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']

            # 预处理输入的图片名称
            normalized_name = image_name.strip().lower()

            # 策略1: 精确匹配（如果已包含扩展名）
            if any(normalized_name.endswith(ext) for ext in image_extensions):
                target_file = folder_path / image_name
                if target_file.exists():
                    log.info(f"精确匹配找到文件: {target_file}")
                    return target_file

            # 策略2: 直接匹配（不带扩展名）
            for ext in image_extensions:
                # 尝试原始名称
                target_file = folder_path / f"{image_name}{ext}"
                if target_file.exists():
                    log.info(f"直接匹配找到文件: {target_file}")
                    return target_file

                # 尝试大写扩展名
                target_file = folder_path / f"{image_name}{ext.upper()}"
                if target_file.exists():
                    log.info(f"直接匹配找到文件（大写扩展名）: {target_file}")
                    return target_file

            # 策略3: 空格替换为下划线匹配
            underscore_name = normalized_name.replace(' ', '_')
            if underscore_name != normalized_name:
                for ext in image_extensions:
                    target_file = folder_path / f"{underscore_name}{ext}"
                    if target_file.exists():
                        log.info(f"下划线替换匹配找到文件: {target_file}")
                        return target_file

                    # 尝试大写扩展名
                    target_file = folder_path / f"{underscore_name}{ext.upper()}"
                    if target_file.exists():
                        log.info(f"下划线替换匹配找到文件（大写扩展名）: {target_file}")
                        return target_file

            # 策略4: 智能模糊匹配
            log.debug(f"开始智能模糊匹配: {image_name}")
            best_match = self._find_best_fuzzy_match(folder_path, normalized_name, image_extensions)
            if best_match:
                log.info(f"智能模糊匹配找到文件: {best_match}")
                return best_match

            # 策略5: 简单包含匹配（兜底策略）
            log.debug(f"开始简单包含匹配: {image_name}")
            for file_path in folder_path.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                    file_stem_lower = file_path.stem.lower()
                    if normalized_name in file_stem_lower:
                        log.info(f"简单包含匹配找到文件: {file_path}")
                        return file_path

            log.warning(f"未找到匹配的图片文件: {image_name}")
            return None

        except Exception as e:
            log.error(f"查找ask_screen图片异常: {e}")
            return None

    def _find_best_fuzzy_match(self, folder_path: Path, normalized_name: str, image_extensions: List[str]) -> Optional[Path]:
        """
        智能模糊匹配，使用多种策略找到最佳匹配

        Args:
            folder_path: 文件夹路径
            normalized_name: 标准化的图片名称
            image_extensions: 支持的图片扩展名列表

        Returns:
            Optional[Path]: 最佳匹配的文件路径
        """
        try:
            # 将输入名称分割为关键词
            keywords = normalized_name.replace('_', ' ').split()
            if not keywords:
                return None

            best_match = None
            best_score = 0

            for file_path in folder_path.iterdir():
                if not file_path.is_file() or file_path.suffix.lower() not in image_extensions:
                    continue

                file_stem_lower = file_path.stem.lower()

                # 计算匹配分数
                score = self._calculate_match_score(file_stem_lower, keywords, normalized_name)

                if score > best_score:
                    best_score = score
                    best_match = file_path

            # 只有当匹配分数足够高时才返回结果
            if best_score >= 0.6:  # 60% 匹配度阈值
                log.debug(f"最佳匹配文件: {best_match}, 匹配分数: {best_score:.2f}")
                return best_match

            return None

        except Exception as e:
            log.error(f"智能模糊匹配异常: {e}")
            return None

    def _calculate_match_score(self, file_stem: str, keywords: List[str], full_name: str) -> float:
        """
        计算文件名与搜索关键词的匹配分数

        Args:
            file_stem: 文件名（不含扩展名）
            keywords: 搜索关键词列表
            full_name: 完整的搜索名称

        Returns:
            float: 匹配分数 (0.0 - 1.0)
        """
        try:
            # 将文件名标准化
            file_words = file_stem.replace('_', ' ').replace('-', ' ').split()

            # 策略1: 完全匹配
            if full_name == file_stem:
                return 1.0

            # 策略2: 包含完整名称
            if full_name in file_stem:
                return 0.9

            # 策略3: 关键词匹配
            matched_keywords = 0
            for keyword in keywords:
                if keyword in file_stem:
                    matched_keywords += 1
                else:
                    # 检查部分匹配
                    for file_word in file_words:
                        if keyword in file_word or file_word in keyword:
                            matched_keywords += 0.5
                            break

            keyword_score = matched_keywords / len(keywords) if keywords else 0

            # 策略4: 词序匹配奖励
            sequence_bonus = 0
            if len(keywords) > 1:
                file_text = ' '.join(file_words)
                search_text = ' '.join(keywords)
                if search_text in file_text:
                    sequence_bonus = 0.2

            # 策略5: 长度相似性奖励
            length_similarity = min(len(full_name), len(file_stem)) / max(len(full_name), len(file_stem))
            length_bonus = length_similarity * 0.1

            total_score = keyword_score + sequence_bonus + length_bonus
            return min(total_score, 1.0)

        except Exception as e:
            log.error(f"计算匹配分数异常: {e}")
            return 0.0
    
    def _get_document_files(self, folder_path: Path) -> List[Path]:
        """获取文档文件列表"""
        document_extensions = ['.txt', '.pdf', '.doc', '.docx', '.rtf', '.odt']
        files = []
        
        for ext in document_extensions:
            files.extend(folder_path.glob(f"*{ext}"))
            files.extend(folder_path.glob(f"*{ext.upper()}"))
        
        return files
    
    def _get_image_files(self, folder_path: Path) -> List[Path]:
        """获取图片文件列表"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        files = []
        
        for ext in image_extensions:
            files.extend(folder_path.glob(f"*{ext}"))
            files.extend(folder_path.glob(f"*{ext.upper()}"))
        
        return files
    
    def _ensure_target_directory(self, target_path: str) -> bool:
        """确保目标目录存在"""
        try:
            cmd = f"adb shell mkdir -p {target_path}"
            result = self._run_adb_command(cmd)

            if result.returncode == 0:
                log.debug(f"目标目录已确保存在: {target_path}")
                return True
            else:
                log.error(f"创建目标目录失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"确保目标目录存在时出错: {e}")
            return False
    
    def _push_single_file(self, file_path: Path, target_path: str) -> bool:
        """推送单个文件"""
        try:
            target_file = f"{target_path}/{file_path.name}"
            cmd = f'adb push "{file_path}" "{target_file}"'

            log.debug(f"推送文件: {file_path.name} -> {target_file}")
            result = self._run_adb_command(cmd)

            if result.returncode == 0:
                log.info(f"✅ 文件推送成功: {file_path.name}")

                # 设置文件权限，确保相册能访问
                self._set_file_permissions(target_file)

                return True
            else:
                log.error(f"❌ 文件推送失败: {file_path.name}, 错误: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"推送文件时出错: {e}")
            return False
    
    def check_device_connection(self) -> bool:
        """检查设备连接状态"""
        try:
            result = self._run_adb_command("adb devices")

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                devices = [line for line in lines[1:] if line.strip() and 'device' in line]

                if devices:
                    log.info(f"检测到 {len(devices)} 个连接的设备")
                    return True
                else:
                    log.error("未检测到连接的设备")
                    return False
            else:
                log.error(f"检查设备连接失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查设备连接时出错: {e}")
            return False
    
    def clean_target_directory(self, target_path: str, file_pattern: str = "*") -> bool:
        """清理目标目录中的文件"""
        try:
            cmd = f"adb shell rm -f {target_path}/{file_pattern}"
            result = self._run_adb_command(cmd)

            if result.returncode == 0:
                log.info(f"✅ 清理目标目录成功: {target_path}")
                return True
            else:
                log.warning(f"清理目标目录可能失败: {result.stderr}")
                return True  # 即使失败也继续，可能是目录不存在

        except Exception as e:
            log.error(f"清理目标目录时出错: {e}")
            return False

    def batch_clean_directories(self, directories: Optional[List[dict]] = None, file_pattern: str = "*") -> dict:
        """
        批量清理多个目录中的文件

        Args:
            directories: 目录配置列表，每个元素为字典，包含path和description字段
                        如果为None，则使用默认的常用目录列表
            file_pattern: 文件匹配模式，默认为"*"（所有文件）

        Returns:
            dict: 清理结果统计，包含成功、失败的目录数量和详细信息
        """
        try:
            log.info("开始批量清理目录")

            # 如果没有指定目录，使用默认的常用目录
            if directories is None:
                directories = self._get_default_clean_directories()

            # 验证目录配置格式
            if not self._validate_directories_config(directories):
                log.error("目录配置格式错误")
                return {"success": False, "error": "目录配置格式错误"}

            # 执行批量清理
            results = {
                "total": len(directories),
                "success_count": 0,
                "failed_count": 0,
                "success_dirs": [],
                "failed_dirs": [],
                "details": []
            }

            for dir_config in directories:
                path = dir_config["path"]
                description = dir_config.get("description", path)

                log.info(f"正在清理: {description} ({path})")

                # 清理单个目录
                success = self.clean_target_directory(path, file_pattern)

                detail = {
                    "path": path,
                    "description": description,
                    "success": success
                }

                if success:
                    results["success_count"] += 1
                    results["success_dirs"].append(path)
                    log.info(f"✅ {description} 清理成功")
                else:
                    results["failed_count"] += 1
                    results["failed_dirs"].append(path)
                    log.warning(f"❌ {description} 清理失败")

                results["details"].append(detail)

                # 添加短暂延迟，避免命令执行过快
                time.sleep(0.5)

            # 输出总结
            log.info(f"批量清理完成: 成功 {results['success_count']}/{results['total']} 个目录")

            if results["failed_count"] > 0:
                log.warning(f"失败的目录: {', '.join(results['failed_dirs'])}")


            # 刷新媒体库，让系统能识别新文件
            target_path: str = "/sdcard/Download"
            self._refresh_media_library(target_path)

            results["success"] = True
            return results

        except Exception as e:
            log.error(f"批量清理目录时出错: {e}")
            return {
                "success": False,
                "error": str(e),
                "total": 0,
                "success_count": 0,
                "failed_count": 0
            }

    def _get_default_clean_directories(self) -> List[dict]:
        """
        获取默认的清理目录列表

        Returns:
            List[dict]: 默认目录配置列表
        """
        return [
            {
                "path": "/sdcard/DCIM/Camera",
                "description": "相机拍照目录"
            },
            {
                "path": "/sdcard/Pictures/Screenshot",
                "description": "截图目录"
            },
            {
                "path": "/sdcard/Movies/ScreenRecord",
                "description": "录屏目录"
            },
            {
                "path": "/sdcard/Download",
                "description": "下载目录"
            }
        ]

    def _validate_directories_config(self, directories: List[dict]) -> bool:
        """
        验证目录配置格式

        Args:
            directories: 目录配置列表

        Returns:
            bool: 配置是否有效
        """
        try:
            if not isinstance(directories, list):
                log.error("目录配置必须是列表格式")
                return False

            if not directories:
                log.error("目录配置列表不能为空")
                return False

            for i, dir_config in enumerate(directories):
                if not isinstance(dir_config, dict):
                    log.error(f"目录配置项 {i} 必须是字典格式")
                    return False

                if "path" not in dir_config:
                    log.error(f"目录配置项 {i} 缺少必需的 'path' 字段")
                    return False

                if not isinstance(dir_config["path"], str) or not dir_config["path"].strip():
                    log.error(f"目录配置项 {i} 的 'path' 字段必须是非空字符串")
                    return False

            return True

        except Exception as e:
            log.error(f"验证目录配置时出错: {e}")
            return False
    
    def list_device_files(self, target_path: str) -> List[str]:
        """列出设备目录中的文件"""
        try:
            cmd = f"adb shell ls {target_path}"
            result = self._run_adb_command(cmd)

            if result.returncode == 0:
                files = [f.strip() for f in result.stdout.split('\n') if f.strip()]
                log.debug(f"设备目录 {target_path} 中的文件: {files}")
                return files
            else:
                log.warning(f"列出设备文件失败: {result.stderr}")
                return []

        except Exception as e:
            log.error(f"列出设备文件时出错: {e}")
            return []

    def _refresh_media_library(self, target_path: str) -> bool:
        """刷新媒体库，让相册能看到新推送的图片"""
        try:
            log.info(f"刷新媒体库: {target_path}")

            # 方法1: 使用媒体扫描器扫描指定目录
            scan_cmd = f"adb shell am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file://{target_path}"
            result = self._run_adb_command(scan_cmd)

            if result.returncode == 0:
                log.info("✅ 媒体扫描器扫描成功")
            else:
                log.warning(f"媒体扫描器扫描失败: {result.stderr}")

            # 方法2: 刷新整个媒体库
            refresh_cmd = "adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard"
            result2 = self._run_adb_command(refresh_cmd)

            if result2.returncode == 0:
                log.info("✅ 媒体库刷新成功")
            else:
                log.warning(f"媒体库刷新失败: {result2.stderr}")

            # 方法3: 重启媒体存储服务
            restart_cmd = "adb shell am force-stop com.android.providers.media"
            result3 = self._run_adb_command(restart_cmd)

            if result3.returncode == 0:
                log.info("✅ 媒体存储服务重启成功")
            else:
                log.warning(f"媒体存储服务重启失败: {result3.stderr}")

            # 等待媒体库更新
            time.sleep(3)

            return True

        except Exception as e:
            log.error(f"刷新媒体库失败: {e}")
            return False

    def _set_file_permissions(self, file_path: str) -> bool:
        """设置文件权限，确保相册能访问"""
        try:
            log.debug(f"设置文件权限: {file_path}")

            # 设置文件为可读权限
            chmod_cmd = f"adb shell chmod 644 {file_path}"
            result = self._run_adb_command(chmod_cmd)

            if result.returncode == 0:
                log.debug("✅ 文件权限设置成功")
                return True
            else:
                log.warning(f"文件权限设置失败: {result.stderr}")
                return False

        except Exception as e:
            log.warning(f"设置文件权限时出错: {e}")
            return False


# 全局实例
file_pusher = FilePusher()


def push_test_documents(target_path: str = "/sdcard/Download") -> bool:
    """推送测试文档的便捷函数"""
    return file_pusher.push_documents_to_device(target_path)


def push_test_images(target_path: str = "/sdcard/DCIM/Camera") -> bool:
    """推送测试图片的便捷函数"""
    return file_pusher.push_images_to_device(target_path)


def batch_clean_common_directories(file_pattern: str = "*") -> dict:
    """
    批量清理常用目录的便捷函数

    Args:
        file_pattern: 文件匹配模式，默认为"*"（所有文件）

    Returns:
        dict: 清理结果统计
    """
    return file_pusher.batch_clean_directories(file_pattern=file_pattern)


def batch_clean_custom_directories(directories: List[dict], file_pattern: str = "*") -> dict:
    """
    批量清理自定义目录的便捷函数

    Args:
        directories: 自定义目录配置列表
        file_pattern: 文件匹配模式，默认为"*"（所有文件）

    Returns:
        dict: 清理结果统计
    """
    return file_pusher.batch_clean_directories(directories, file_pattern)


if __name__ == "__main__":
    # 测试文件推送功能
    pusher = FilePusher()

    # 检查设备连接
    if pusher.check_device_connection():
        print("=== 文件推送和清理工具测试 ===")

        # 示例1: 推送ask_screen图片
        pusher.push_ask_screen_image("add the lucy‘s number")

        # 示例2: 推送文档和图片
        # pusher.push_documents_to_device()
        # pusher.push_images_to_device()

        # 示例3: 批量清理常用目录（推荐使用）
        # print("\n--- 批量清理常用目录 ---")
        # result = pusher.batch_clean_directories()
        # print(f"清理结果: 成功 {result['success_count']}/{result['total']} 个目录")

        # 示例4: 清理特定文件类型
        # print("\n--- 清理特定文件类型 ---")
        # result = pusher.batch_clean_directories(file_pattern="*.jpg")
        # print(f"清理JPG文件结果: 成功 {result['success_count']}/{result['total']} 个目录")

        # 示例5: 自定义目录清理
        # custom_dirs = [
        #     {"path": "/sdcard/Music", "description": "音乐目录"},
        #     {"path": "/sdcard/Documents", "description": "文档目录"}
        # ]
        # result = pusher.batch_clean_directories(custom_dirs)
        # print(f"自定义目录清理结果: 成功 {result['success_count']}/{result['total']} 个目录")

        # 示例6: 单个目录清理（原有方法）
        # pusher.clean_target_directory("/sdcard/Download", "*.txt")

    else:
        print("请确保设备已连接并启用USB调试")
