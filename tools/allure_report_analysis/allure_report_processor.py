#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Allure报告综合处理工具
整合Excel生成、失败分析、日志压缩和归档功能

功能：
1. 调用allure_to_excel生成Excel表格
2. 调用excel_failure_analyzer分析结果
3. 压缩日志文件
4. 日志归档到网络目录

作者: AI Assistant
创建时间: 2025-09-02
"""

import os
import sys
import zipfile
import shutil
import yaml
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import argparse

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

# 导入现有工具
from tools.allure_report_analysis.allure_to_excel import AllureToExcelConverter
from tools.allure_report_analysis.excel_failure_analyzer import ExcelFailureAnalyzer
from tools.allure_report_analysis.excel_to_mysql import ExcelToMySQLConverter
from tools.feishu_tools.excel_auto_uploader import ExcelAutoUploader


class AllureReportProcessor:
    """Allure报告综合处理器"""
    
    def __init__(self, reports_dir: str = None, archive_base_dir: str = None):
        """
        初始化处理器
        
        Args:
            reports_dir: 报告目录路径，默认为项目根目录下的reports
            archive_base_dir: 归档基础目录，默认为网络路径
        """
        # 设置报告目录
        if reports_dir is None:
            self.reports_dir = project_root / "reports"
        else:
            self.reports_dir = Path(reports_dir)
            
        self.allure_results_dir = self.reports_dir / "allure-results"
        
        # 设置归档目录
        if archive_base_dir is None:
            self.archive_base_dir = Path(r"\\***********\sw_log\软件产品测试Log\AI语音助手")
        else:
            self.archive_base_dir = Path(archive_base_dir)
            
        # 获取当前日期
        self.current_date = datetime.now().strftime("%Y%m%d")
        self.current_datetime = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 设置临时目录
        self.temp_dir = project_root / "temp"
        self.temp_dir.mkdir(exist_ok=True)
        
        print(f"📁 报告目录: {self.reports_dir}")
        print(f"📁 Allure结果目录: {self.allure_results_dir}")
        print(f"📁 归档目录: {self.archive_base_dir}")
        print(f"📁 临时目录: {self.temp_dir}")

    def _load_device_info(self) -> Dict[str, Any]:
        """
        从devices.yaml加载当前设备信息

        Returns:
            Dict[str, Any]: 设备信息字典
        """
        try:
            devices_config_path = project_root / "config" / "devices.yaml"
            with open(devices_config_path, 'r', encoding='utf-8') as f:
                devices_config = yaml.safe_load(f)

            current_device_key = devices_config.get('current_device', 'current_device')
            device_info = devices_config.get('devices', {}).get(current_device_key, {})

            return device_info
        except Exception as e:
            print(f"加载设备信息失败: {e}")
            return {
                'device_id': 'Unknown',
                'device_name': 'Unknown Device',
                'android_version': 'Unknown',
                'brand': 'Unknown',
                'model': 'Unknown',
                'platform_version': 'Unknown'
            }
    
    def step1_generate_excel_report(self) -> Optional[str]:
        """
        步骤1: 生成Excel报告
        
        Returns:
            str: Excel文件路径，失败返回None
        """
        print("\n" + "="*50)
        print("步骤1: 生成Excel报告")
        print("="*50)
        
        try:
            converter = AllureToExcelConverter(str(self.reports_dir))
            excel_path = converter.convert_to_excel()
            
            if excel_path and os.path.exists(excel_path):
                print(f"✅ Excel报告生成成功: {excel_path}")
                return excel_path
            else:
                print("❌ Excel报告生成失败")
                return None
                
        except Exception as e:
            print(f"❌ 生成Excel报告时出错: {e}")
            return None
    
    def step2_analyze_failures(self, excel_path: str) -> Optional[str]:
        """
        步骤2: 分析失败用例
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            str: 分析报告路径，失败返回None
        """
        print("\n" + "="*50)
        print("步骤2: 分析失败用例")
        print("="*50)
        
        try:
            analyzer = ExcelFailureAnalyzer(excel_path)
            
            if not analyzer.load_excel():
                print("❌ 加载Excel文件失败")
                return None
                
            failed_records = analyzer.analyze_failures()
            
            if failed_records:
                # 设置输出路径到当前工具目录
                output_dir = current_dir / "failure_analysis"
                output_dir.mkdir(exist_ok=True)
                
                base_name = os.path.splitext(os.path.basename(excel_path))[0]
                analysis_path = output_dir / f"failure_analysis_{base_name}.xlsx"
                
                result_path = analyzer.generate_failure_report(str(analysis_path))
                
                if result_path:
                    print(f"✅ 失败分析报告生成成功: {result_path}")
                    return result_path
                else:
                    print("❌ 失败分析报告生成失败")
                    return None
            else:
                print("✅ 没有失败用例，跳过分析步骤")
                return "no_failures"
                
        except Exception as e:
            print(f"❌ 分析失败用例时出错: {e}")
            return None
    
    def step3_compress_logs(self) -> Optional[str]:
        """
        步骤3: 压缩日志文件

        Returns:
            str: 压缩文件路径，失败返回None
        """
        print("\n" + "="*50)
        print("步骤3: 压缩日志文件")
        print("="*50)

        try:
            # 检查allure-results目录
            if not self.allure_results_dir.exists():
                print(f"❌ Allure结果目录不存在: {self.allure_results_dir}")
                return None

            # 获取设备信息
            device_info = self._load_device_info()
            device_id = device_info.get('device_id', 'Unknown')

            # 创建压缩文件名：{deviceID}_{当前日期}
            zip_filename = f"{device_id}_{self.current_date}.zip"
            zip_path = self.temp_dir / zip_filename

            print(f"📦 开始压缩日志文件到: {zip_path}")
            print(f"📱 设备ID: {device_id}")
            print(f"📅 日期: {self.current_date}")

            # 创建ZIP文件
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 遍历allure-results目录中的所有文件
                for file_path in self.allure_results_dir.rglob('*'):
                    if file_path.is_file():
                        # 计算相对路径
                        arcname = file_path.relative_to(self.allure_results_dir)
                        zipf.write(file_path, arcname)

            # 检查压缩文件大小
            if zip_path.exists():
                file_size = zip_path.stat().st_size
                file_size_mb = file_size / (1024 * 1024)
                print(f"✅ 日志压缩完成")
                print(f"📦 压缩文件: {zip_path}")
                print(f"📊 文件大小: {file_size_mb:.2f} MB")
                return str(zip_path)
            else:
                print("❌ 压缩文件创建失败")
                return None
                
        except Exception as e:
            print(f"❌ 压缩日志文件时出错: {e}")
            return None
    
    def step4_archive_logs(self, zip_path: str) -> bool:
        """
        步骤4: 归档日志到网络目录
        
        Args:
            zip_path: 压缩文件路径
            
        Returns:
            bool: 归档是否成功
        """
        print("\n" + "="*50)
        print("步骤4: 归档日志文件")
        print("="*50)
        
        try:
            # 检查网络目录是否可访问
            if not self.archive_base_dir.exists():
                print(f"❌ 归档目录不存在或无法访问: {self.archive_base_dir}")
                return False
                
            # 创建目标文件路径
            zip_filename = os.path.basename(zip_path)
            target_path = self.archive_base_dir / zip_filename
            
            print(f"📤 开始上传文件到: {target_path}")
            
            # 复制文件到网络目录
            shutil.copy2(zip_path, target_path)
            
            # 验证文件是否成功复制
            if target_path.exists():
                source_size = os.path.getsize(zip_path)
                target_size = os.path.getsize(target_path)
                
                if source_size == target_size:
                    print(f"✅ 文件归档成功")
                    print(f"📤 目标路径: {target_path}")
                    print(f"📊 文件大小: {source_size} 字节")
                    return True
                else:
                    print(f"❌ 文件大小不匹配，归档可能失败")
                    print(f"源文件大小: {source_size} 字节")
                    print(f"目标文件大小: {target_size} 字节")
                    return False
            else:
                print("❌ 目标文件不存在，归档失败")
                return False
                
        except Exception as e:
            print(f"❌ 归档日志文件时出错: {e}")
            return False

    def step5_upload_to_feishu(self, excel_path: str, analysis_path: str) -> bool:
        """
        步骤5: 将执行结果和分析结果上传至飞书

        Args:
            excel_path: Excel文件路径
            analysis_path: 分析报告路径

        Returns:
            bool: 上传是否成功
        """
        print("\n" + "="*50)
        print("步骤5: 将执行结果和分析结果上传至飞书")
        print("="*50)

        try:
            # 创建上传器实例
            uploader = ExcelAutoUploader()

            # 执行上传任务
            results = uploader.upload_all_latest_excel()

            # 根据结果设置退出码
            success_count = sum(1 for success in results.values() if success)
            if success_count == len(results):
                print("所有文件上传成功！")
                return True

            elif success_count > 0:
                print("部分文件上传成功")
                return True

            else:
                print("所有文件上传失败")
                return False


        except Exception as e:
            print(f"程序执行异常: {e}")

    
    def process_all(self) -> Dict[str, Any]:
        """
        执行完整的处理流程
        
        Returns:
            Dict[str, Any]: 处理结果摘要
        """
        print("🚀 开始Allure报告综合处理流程")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {
            'start_time': datetime.now(),
            'excel_path': None,
            'analysis_path': None,
            'zip_path': None,
            'archive_success': False,
            'success': False,
            'error_message': None
        }
        
        try:
            # 步骤1: 生成Excel报告
            excel_path = self.step1_generate_excel_report()
            if not excel_path:
                results['error_message'] = "Excel报告生成失败"
                return results
            results['excel_path'] = excel_path
            
            # 步骤2: 分析失败用例
            analysis_path = self.step2_analyze_failures(excel_path)
            if analysis_path is None:
                results['error_message'] = "失败用例分析失败"
                return results
            results['analysis_path'] = analysis_path
            
            # 步骤3: 压缩日志文件
            zip_path = self.step3_compress_logs()
            if not zip_path:
                results['error_message'] = "日志压缩失败"
                return results
            results['zip_path'] = zip_path

            # 新增步骤：将执行结果和分析结果上传至飞书
            upload_success = self.step5_upload_to_feishu(excel_path, analysis_path)
            if not upload_success:
                results['error_message'] = "飞书上传失败"
                return results
            
            # 步骤4: 归档日志文件
            archive_success = self.step4_archive_logs(zip_path)
            results['archive_success'] = archive_success
            
            if not archive_success:
                results['error_message'] = "日志归档失败"
                return results

            # 所有步骤成功
            results['success'] = True
            results['end_time'] = datetime.now()
            
            # 打印成功摘要
            self._print_success_summary(results)
            
            return results
            
        except Exception as e:
            results['error_message'] = f"处理过程中出现异常: {e}"
            print(f"❌ 处理失败: {e}")
            return results
    
    def _print_success_summary(self, results: Dict[str, Any]):
        """打印成功摘要"""
        print("\n" + "="*60)
        print("🎉 Allure报告处理完成！")
        print("="*60)
        
        duration = results['end_time'] - results['start_time']
        print(f"⏱️  总耗时: {duration.total_seconds():.1f} 秒")
        print(f"📊 Excel报告: {results['excel_path']}")
        
        if results['analysis_path'] != "no_failures":
            print(f"📈 失败分析: {results['analysis_path']}")
        else:
            print("📈 失败分析: 无失败用例")
            
        print(f"📦 压缩文件: {results['zip_path']}")
        print(f"📤 归档状态: {'成功' if results['archive_success'] else '失败'}")
        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Allure报告综合处理工具')
    parser.add_argument('--reports-dir', '-r', default=None,
                        help='报告目录路径 (默认: 项目根目录/reports)')
    parser.add_argument('--archive-dir', '-a', default=None,
                        help='归档目录路径 (默认: \\\\***********\\sw_log\\软件产品测试Log\\AI语音助手)')
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = AllureReportProcessor(
        reports_dir=args.reports_dir,
        archive_base_dir=args.archive_dir
    )
    
    # 执行处理流程
    results = processor.process_all()
    
    # 返回适当的退出码
    return 0 if results['success'] else 1


if __name__ == "__main__":
    exit(main())
