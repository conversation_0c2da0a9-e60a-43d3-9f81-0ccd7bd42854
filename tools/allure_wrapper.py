#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Allure命令包装器
自动使用正确的Allure路径
"""

import subprocess
import sys
import os

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
ALLURE_PATH = os.path.abspath(os.path.join(ROOT_DIR, "utils/allure-2.34.0/bin/allure.bat"))
# ALLURE_PATH = r"D:\PythonProject\app_test\utils\allure-2.34.0\bin\allure.bat"

def main():
    """主函数"""
    try:
        # 检查Allure路径是否存在
        if not os.path.exists(ALLURE_PATH):
            print(f"Allure路径不存在: {ALLURE_PATH}")
            sys.exit(1)

        # 构建命令
        cmd = [ALLURE_PATH] + sys.argv[1:]
        print(f"执行命令: {' '.join(cmd)}")

        # 执行命令
        result = subprocess.run(cmd, cwd=".", text=True, capture_output=False)
        sys.exit(result.returncode)

    except Exception as e:
        print(f"执行Allure命令失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
